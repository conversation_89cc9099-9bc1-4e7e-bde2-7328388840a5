<?php

namespace Database\Factories;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InvoiceItem>
 */
class InvoiceItemFactory extends Factory
{
    protected $model = InvoiceItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $price = $this->faker->randomFloat(2, 1, 100);
        $qty = $this->faker->numberBetween(1, 10);
        $netAmount = $price * $qty;

        return [
            'invoice_id' => Invoice::factory(),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}-[0-9]{4}'),
            'article_net_price' => $price,
            'article_unit_code' => 'STK',
            'article_code_supplier' => $this->faker->regexify('[A-Z]{3}-[0-9]{4}'),
            'vat_base_amount' => $netAmount,
            'vat_percentage' => 21,
            'gtin' => $this->faker->numberBetween(1000000000, 9999999999),
            'invoiced_qty' => $qty,
            'net_line_amount' => $netAmount,
        ];
    }
}
