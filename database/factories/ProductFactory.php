<?php
declare(strict_types=1);

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'sku' => $this->faker->unique()->bothify('SKU###???'),
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph,
            'transport_unit' => $this->faker->randomElement(['EP', 'BP']),
            'gtin' => $this->faker->optional()->numberBetween(1000000000, 9999999999),
//            'price' => $this->faker->randomFloat(2, 1, 1000), // random float between 1 and 1000
//            'cost' => $this->faker->randomFloat(2, 1, 1000), // random float between 1 and 1000
        ];
    }
}