<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItem>
 */
class OrderItemFactory extends Factory
{
    protected $model = OrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $priceExVat = $this->faker->randomFloat(2, 1, 100);
        $vat = ($priceExVat / 100) * 21;
        $priceInclVat = $priceExVat + $vat;

        return [
            'order_id' => Order::factory(),
            'product_id' => Product::factory(),
            'sku' => $this->faker->regexify('[A-Z]{3}-[0-9]{4}'),
            'qty' => $this->faker->numberBetween(1, 10),
            'product_name' => $this->faker->words(3, true),
            'product_desc' => $this->faker->optional()->sentence(),
            'cost' => $this->faker->randomFloat(2, 1, 80),
            'price_ex_vat' => $priceExVat,
            'price_incl_vat' => $priceInclVat,
            'vat' => $vat,
            'order' => $this->faker->numberBetween(1, 100),
        ];
    }
}
