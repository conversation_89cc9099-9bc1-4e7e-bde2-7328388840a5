<?php

namespace Database\Factories;

use App\Models\Invoice;
use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $netAmount = $this->faker->randomFloat(2, 10, 1000);
        $vatPercentage = 21;
        $vatAmount = ($netAmount / 100) * $vatPercentage;
        $total = $netAmount + $vatAmount;

        return [
            'order_id' => $this->faker->optional(0.8)->passthrough(Order::factory()), // 80% chance of having an order_id
            'invoice_nr' => $this->faker->unique()->numberBetween(2024001, 2024999),
            'order_nr_exact' => $this->faker->numberBetween(1, 9999),
            'vat_prct' => $vatPercentage,
            'vat_amount' => $vatAmount,
            'total' => $total,
            'net_line_amount' => $netAmount,
            'invoice_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'sent' => false,
        ];
    }
}
