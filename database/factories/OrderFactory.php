<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_id' => Customer::factory(),
            'user_id' => User::factory(),
            'status' => 'PROCESSING',
            'cost' => $this->faker->randomFloat(2, 10, 1000),
            'transport_cost' => $this->faker->randomFloat(2, 0, 50),
            'total_cost' => function (array $attributes) {
                return $attributes['cost'] + $attributes['transport_cost'];
            },
            'subtotal' => function (array $attributes) {
                return $attributes['cost'];
            },
            'total_ex_vat' => function (array $attributes) {
                return $attributes['cost'];
            },
            'transport_price' => function (array $attributes) {
                return $attributes['transport_cost'];
            },
            'vat' => function (array $attributes) {
                return ($attributes['total_ex_vat'] / 100) * 21;
            },
            'total_incl_vat' => function (array $attributes) {
                return $attributes['total_ex_vat'] + (($attributes['total_ex_vat'] / 100) * 21);
            },
            'delivery_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'order_date' => $this->faker->dateTimeBetween('-30 days', 'now')->format('Y-m-d'),
            'shipper' => $this->faker->randomElement(['dhl', 'ups', 'pickup']),
            'reference' => $this->faker->optional()->word(),
        ];
    }
}
