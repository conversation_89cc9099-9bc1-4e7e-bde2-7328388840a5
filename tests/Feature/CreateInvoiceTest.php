<?php

namespace Tests\Feature;

use App\Livewire\CreateInvoice;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class CreateInvoiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_render_create_invoice_component()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->assertSee('Factuur Aanmaken')
            ->assertDontSee('Er bestaat al een factuur');
    }

    /** @test */
    public function it_shows_warning_when_invoice_already_exists()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);
        Invoice::factory()->create(['order_id' => $order->id]);

        // Refresh the order to ensure the hasInvoice attribute is calculated correctly
        $order->refresh();

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->call('openModal')
            ->assertSee('Er bestaat al een factuur voor deze bestelling');
    }

    /** @test */
    public function it_can_open_and_close_modal()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->assertSet('showModal', false)
            ->call('openModal')
            ->assertSet('showModal', true)
            ->call('closeModal')
            ->assertSet('showModal', false);
    }

    /** @test */
    public function it_validates_invoice_number_is_required()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->set('invoiceNumber', '')
            ->call('createInvoice')
            ->assertHasErrors(['invoiceNumber' => 'required']);
    }

    /** @test */
    public function it_validates_invoice_number_is_integer()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->set('invoiceNumber', 'not-a-number')
            ->call('createInvoice')
            ->assertHasErrors(['invoiceNumber' => 'integer']);
    }

    /** @test */
    public function it_can_create_invoice_with_items()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'subtotal' => 100.00,
            'total_ex_vat' => 100.00
        ]);
        
        $product = Product::factory()->create(['sku' => 'TEST-SKU', 'gtin' => 1234567890]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 2,
            'price_ex_vat' => 50.00
        ]);

        $this->assertEquals(0, Invoice::count());
        $this->assertEquals(0, InvoiceItem::count());

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->set('invoiceNumber', '2024001')
            ->call('createInvoice');

        $this->assertEquals(1, Invoice::count());
        $this->assertEquals(1, InvoiceItem::count());

        $invoice = Invoice::first();
        $this->assertEquals($order->id, $invoice->order_id);
        $this->assertEquals('2024001', $invoice->invoice_nr);
        $this->assertEquals(21, $invoice->vat_prct);
        $this->assertEquals(21.00, $invoice->vat_amount); // 21% of 100
        $this->assertEquals(121.00, $invoice->total); // 100 + 21
        $this->assertEquals(100.00, $invoice->net_line_amount);

        $invoiceItem = InvoiceItem::first();
        $this->assertEquals($invoice->id, $invoiceItem->invoice_id);
        $this->assertEquals('TEST-SKU', $invoiceItem->sku);
        $this->assertEquals(50.00, $invoiceItem->article_net_price); // 100.00 / 2 = 50.00
        $this->assertEquals(2, $invoiceItem->invoiced_qty);
        $this->assertEquals(100.00, $invoiceItem->net_line_amount); // 2 * 50
    }

    /** @test */
    public function it_deletes_existing_invoice_when_creating_new_one()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create(['customer_id' => $customer->id]);
        
        // Create existing invoice with items
        $existingInvoice = Invoice::factory()->create(['order_id' => $order->id]);
        InvoiceItem::factory()->create(['invoice_id' => $existingInvoice->id]);

        $this->assertEquals(1, Invoice::count());
        $this->assertEquals(1, InvoiceItem::count());

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->set('invoiceNumber', '2024002')
            ->call('createInvoice');

        // Should still have 1 invoice and 0 items (old ones deleted, new ones created)
        $this->assertEquals(1, Invoice::count());
        
        $newInvoice = Invoice::first();
        $this->assertEquals('2024002', $newInvoice->invoice_nr);
        $this->assertNotEquals($existingInvoice->id, $newInvoice->id);
    }

    /** @test */
    public function it_calculates_article_net_price_correctly()
    {
        $customer = Customer::factory()->create();
        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'subtotal' => 150.00,
            'total_ex_vat' => 150.00
        ]);

        $product = Product::factory()->create(['sku' => 'CALC-TEST', 'gtin' => 987654321]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'qty' => 3,
            'price_ex_vat' => 50.00 // Total net line amount will be 150.00
        ]);

        Livewire::test(CreateInvoice::class, ['orderId' => $order->id])
            ->set('invoiceNumber', '2024003')
            ->call('createInvoice');

        $invoiceItem = InvoiceItem::first();

        // Verify the calculation: article_net_price should be net_line_amount / invoiced_qty
        $this->assertEquals(150.00, $invoiceItem->net_line_amount); // 3 * 50.00
        $this->assertEquals(3, $invoiceItem->invoiced_qty);
        $this->assertEquals(50.00, $invoiceItem->article_net_price); // 150.00 / 3 = 50.00

        // Double check the calculation
        $calculatedPrice = $invoiceItem->net_line_amount / $invoiceItem->invoiced_qty;
        $this->assertEquals($calculatedPrice, $invoiceItem->article_net_price);
    }
}
