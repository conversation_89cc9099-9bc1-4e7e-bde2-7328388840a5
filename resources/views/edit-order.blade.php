<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between">
            <div class="flex">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight mr-3">
                    Bestelling: {{ $order->id }}
                </h2>
                <x-info-balloon>
                    <h4>Bestelling</h4>
                    <p>Dit is een enkele bestelling</p>
                    <h5>Besteldatum</h5>
                    <p>Dit is de datum waarop de bestelling is binnen gekomen. Aan de hand van deze datum worden alle prijzen berekend.</p>
                    <h5>Leverdatum</h5>
                    <p>De datum waarop het product geleverd moet worden. De pick datum zal 1 dag hiervoor zijn.</p>
                </x-info-balloon>
            </div>
            <div class="mr-3">
                <livewire:order-status :status="$order->status" :orderId="$order->id" />
            </div>

        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex justify-between" style="height: 200px;">
                    <div id="billing">
                        <strong>Factuuradres</strong><br>
                        {{ $order->customer->exact_id }}<br>
                        {{ $order->customer->name }}<br>
                        {{ $order->customer->address }}<br>
                        {{ $order->customer->postal_code }} {{ $order->customer->city }}<br>
                        {{ $order->customer->email }}
                    </div>
                    <div id="shipping">
                        @if($order->shipper !== 'pickup')
                            <livewire:edit-order-shipping-address :address="$order->address" :hasShipment="$order->hasShipment"/>
                            {{ $order->reference }}
                        @else
                            <strong>Afhalen</strong>
                            <p>Order afgehaald op locatie Winsum.</p>
                        @endif
                    </div>
                    <div id="delivery">
                            <livewire:edit-order-date :orderDate="$order->order_date" :hasShipment="$order->hasShipment" :status="$order->status" />
                            <livewire:edit-order-delivery-date :deliveryDate="$order->delivery_date" :hasShipment="$order->hasShipment" :status="$order->status"/>
                    </div>
                    <div id="shipping">
                        <livewire:ship-order :order="$order->id" />
                    </div>
                </div>
                <div id="orderlines">
                    <livewire:edit-orderlines :order="$order->id" />
                </div>
                <div id="totals">
                    <livewire:edit-order-totals :order="$order" />
                </div>

            </div>

            @if($order->has_credit)
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mt-4 flex">
                    <livewire:order-credit-block :orderId="$order->id" />
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mt-4 flex">
                <div class="w-1/2">
                    <h5>Log</h5>
                    <ul class="list-disc">
                        @if($order->orderLog)
                            {!!  $order->orderLog->log !!}
                        @endif
                    </ul>
                </div>
                <div class="w-1/2">
                    <livewire:order-comments :order="$order" />
                </div>
            </div>

            @if($order->hasInvoice)
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mt-4 flex">
                    <livewire:order-invoice :orderId="$order->id" />
                </div>
            @endif
{{--            @if(!$order->has_credit)--}}
{{--            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mt-4 flex">--}}
{{--                <x-secondary-button onclick="window.location = '/order/credit/{{$order->id}}'">Crediteren</x-secondary-button>--}}
{{--            </div>--}}
{{--            @endif--}}

            @if($order->transus)
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mt-4">
                    <div class="flex justify-between items-center mb-4">
                        <h5>Transus bericht</h5>
                        @if($order->shipment)
                            <a href="{{ url('createtransusmessage/' . $order->id) }}"
                               class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-black uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Resend Shipped Message
                            </a>
                        @endif
                    </div>
                    <div class="p-3 bg-gray-100 rounded-4">
                        <pre>{{ $order->transus->message }}</pre>
                    </div>
                </div>
            @endif
            <livewire:delete-order :order="$order->id" />
        </div>
    </div>
</x-app-layout>
