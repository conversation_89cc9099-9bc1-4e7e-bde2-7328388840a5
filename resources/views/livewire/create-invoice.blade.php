<div>
    <!-- Create Invoice Button -->
    <x-button wire:click="openModal" class="bg-green-600 hover:bg-green-700">
        <i class="fa-solid fa-file-invoice mr-2"></i>
        Factuur Aanmaken
    </x-button>

    <!-- Modal -->
    @if($showModal)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                                <i class="fa-solid fa-file-invoice text-green-600"></i>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                    Factuur Aanmaken
                                </h3>
                                <div class="mt-4">
                                    <p class="text-sm text-gray-500 mb-4">
                                        Voer het factuurnummer in voor bestelling #{{ $order->id }}.
                                        @if($order->hasInvoice)
                                            <br><strong class="text-red-600">Let op: Er bestaat al een factuur voor deze bestelling. Deze wordt vervangen.</strong>
                                        @endif
                                    </p>
                                    
                                    <div class="mb-4">
                                        <label for="invoiceNumber" class="block text-sm font-medium text-gray-700 mb-2">
                                            Factuurnummer *
                                        </label>
                                        <input 
                                            type="number" 
                                            id="invoiceNumber"
                                            wire:model="invoiceNumber" 
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            placeholder="Bijv. 2024001"
                                            min="1"
                                        >
                                        @error('invoiceNumber')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Order Summary -->
                                    <div class="bg-gray-50 p-3 rounded-md">
                                        <h4 class="text-sm font-medium text-gray-700 mb-2">Bestelling Overzicht:</h4>
                                        <div class="text-sm text-gray-600">
                                            <div class="flex justify-between">
                                                <span>Klant:</span>
                                                <span>{{ $order->customer->name }}</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Subtotaal:</span>
                                                <span>&euro; {{ number_format($order->subtotal ?? 0, 2, ',', '.') }}</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>BTW (21%):</span>
                                                <span>&euro; {{ number_format((($order->total_ex_vat ?? $order->subtotal) / 100) * 21, 2, ',', '.') }}</span>
                                            </div>
                                            <div class="flex justify-between font-medium border-t pt-1 mt-1">
                                                <span>Totaal:</span>
                                                <span>&euro; {{ number_format(($order->total_ex_vat ?? $order->subtotal) + ((($order->total_ex_vat ?? $order->subtotal) / 100) * 21), 2, ',', '.') }}</span>
                                            </div>
                                            <div class="flex justify-between text-xs mt-1">
                                                <span>Aantal artikelen:</span>
                                                <span>{{ $order->orderitems->count() }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button 
                            type="button" 
                            wire:click="createInvoice"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            <i class="fa-solid fa-check mr-2"></i>
                            Factuur Aanmaken
                        </button>
                        <button 
                            type="button" 
                            wire:click="closeModal"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Annuleren
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
