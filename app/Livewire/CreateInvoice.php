<?php

namespace App\Livewire;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\InvoiceLink;
use App\Models\Order;
use Carbon\Carbon;
use Livewire\Component;

class CreateInvoice extends Component
{
    public $orderId;
    public $order;
    public $showModal = false;
    public $invoiceNumber = '';

    protected $rules = [
        'invoiceNumber' => 'required|integer|min:1',
    ];

    public function mount($orderId)
    {
        $this->orderId = $orderId;
        $this->order = Order::with('orderitems.product')->find($orderId);
    }

    public function openModal()
    {
        $this->showModal = true;
        $this->invoiceNumber = '';
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->invoiceNumber = '';
        $this->resetValidation();
    }

    public function createInvoice()
    {
        $this->validate();

        // Check if invoice already exists for this order and delete it
        $existingInvoice = Invoice::where('order_id', $this->orderId)->first();
        if ($existingInvoice) {
            // Delete existing invoice items first
            $existingInvoice->invoiceitem()->delete();
            // Delete the invoice
            $existingInvoice->delete();
        }

        // Also check for grouped invoices via InvoiceLink and remove the link
        $existingInvoiceLink = InvoiceLink::where('order_id', $this->orderId)->first();
        if ($existingInvoiceLink) {
            $existingInvoiceLink->delete();
        }

        // Calculate totals from order
        $netAmount = $this->order->total_ex_vat ?? $this->order->subtotal;
        $vatPercentage = 21; // Default VAT percentage
        $vatAmount = ($netAmount / 100) * $vatPercentage;
        $totalAmount = $netAmount + $vatAmount;

        // Create new invoice
        $invoice = Invoice::create([
            'order_id' => $this->orderId,
            'invoice_nr' => $this->invoiceNumber,
            'order_nr_exact' => $this->order->id, // Using order ID as order_nr_exact
            'vat_prct' => $vatPercentage,
            'vat_amount' => $vatAmount,
            'total' => $totalAmount,
            'net_line_amount' => $netAmount,
            'invoice_date' => Carbon::now(),
            'sent' => false,
        ]);

        // Create invoice items from order items
        foreach ($this->order->orderitems as $orderItem) {
            $itemNetPrice = $orderItem->price_ex_vat ?? 0;
            $itemNetAmount = $itemNetPrice * $orderItem->qty;
            
            InvoiceItem::create([
                'invoice_id' => $invoice->id,
                'sku' => $orderItem->product->sku ?? $orderItem->product->id,
                'article_net_price' => $itemNetPrice,
                'article_unit_code' => 'STK', // Default unit code
                'article_code_supplier' => $orderItem->product->sku ?? $orderItem->product->id,
                'vat_base_amount' => $itemNetAmount,
                'vat_percentage' => $vatPercentage,
                'gtin' => $orderItem->product->gtin ?? 0,
                'invoiced_qty' => $orderItem->qty,
                'net_line_amount' => $itemNetAmount,
            ]);
        }

        $this->closeModal();
        
        // Emit event to refresh the page or redirect
        $this->dispatch('invoice-created');
        
        // Show success message
        session()->flash('message', 'Factuur succesvol aangemaakt!');
        
        // Refresh the page to show the new invoice
        return redirect()->route('order.edit', $this->orderId);
    }

    public function render()
    {
        return view('livewire.create-invoice');
    }
}
